<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>API Test Page</h1>
    
    <button onclick="testDashboardAPI()">Test Dashboard API</button>
    <button onclick="testAlertsAPI()">Test Alerts API</button>
    <button onclick="testHealthAPI()">Test Health API</button>
    
    <div id="results"></div>

    <script>
        async function testDashboardAPI() {
            const resultsDiv = document.getElementById('results');
            
            try {
                console.log('Testing dashboard API...');
                const response = await fetch('/api/monitoring/dashboard');
                console.log('Response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('Dashboard data:', data);
                
                resultsDiv.innerHTML += `
                    <div class="test-result success">
                        <h3>✅ Dashboard API Test - SUCCESS</h3>
                        <p>Status: ${response.status}</p>
                        <p>Data Status: ${data.status}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                console.error('Dashboard API test failed:', error);
                resultsDiv.innerHTML += `
                    <div class="test-result error">
                        <h3>❌ Dashboard API Test - FAILED</h3>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function testAlertsAPI() {
            const resultsDiv = document.getElementById('results');
            
            try {
                console.log('Testing alerts API...');
                const response = await fetch('/api/monitoring/alerts');
                console.log('Response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('Alerts data:', data);
                
                resultsDiv.innerHTML += `
                    <div class="test-result success">
                        <h3>✅ Alerts API Test - SUCCESS</h3>
                        <p>Status: ${response.status}</p>
                        <p>Data Status: ${data.status}</p>
                        <p>Alert Count: ${data.data.alertCount}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                console.error('Alerts API test failed:', error);
                resultsDiv.innerHTML += `
                    <div class="test-result error">
                        <h3>❌ Alerts API Test - FAILED</h3>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function testHealthAPI() {
            const resultsDiv = document.getElementById('results');
            
            try {
                console.log('Testing health API...');
                const response = await fetch('/api/monitoring/health');
                console.log('Response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('Health data:', data);
                
                resultsDiv.innerHTML += `
                    <div class="test-result success">
                        <h3>✅ Health API Test - SUCCESS</h3>
                        <p>Status: ${response.status}</p>
                        <p>Health Status: ${data.status}</p>
                        <p>Uptime: ${data.uptime} seconds</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                console.error('Health API test failed:', error);
                resultsDiv.innerHTML += `
                    <div class="test-result error">
                        <h3>❌ Health API Test - FAILED</h3>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }
        
        // Auto-run tests on page load
        window.onload = function() {
            console.log('Running API tests...');
            testHealthAPI();
            setTimeout(testDashboardAPI, 1000);
            setTimeout(testAlertsAPI, 2000);
        };
    </script>
</body>
</html>
