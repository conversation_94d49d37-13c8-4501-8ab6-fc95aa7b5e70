// Monitoring Dashboard JavaScript
async function loadDashboard() {
  console.log("Loading dashboard...");
  document.getElementById("loading").style.display = "block";
  document.getElementById("dashboard").style.display = "none";

  try {
    console.log("Fetching dashboard data...");
    // Load dashboard data
    const response = await fetch("/api/monitoring/dashboard");
    console.log("Dashboard response status:", response.status);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log("Dashboard data:", data);

    if (data.status === "success") {
      renderDashboard(data.data);
      document.getElementById("loading").style.display = "none";
      document.getElementById("dashboard").style.display = "block";
      document.getElementById(
        "last-updated"
      ).textContent = `Last updated: ${new Date().toLocaleString()}`;
    }

    console.log("Fetching alerts...");
    // Load alerts
    const alertsResponse = await fetch("/api/monitoring/alerts");
    console.log("Alerts response status:", alertsResponse.status);

    if (alertsResponse.ok) {
      const alertsData = await alertsResponse.json();
      console.log("Alerts data:", alertsData);

      if (alertsData.status === "success") {
        renderAlerts(alertsData.data);
      }
    }
  } catch (error) {
    console.error("Error loading dashboard:", error);
    document.getElementById(
      "loading"
    ).innerHTML = `<div class="alert alert-critical">Failed to load dashboard data: ${error.message}</div>`;
  }
}

function renderDashboard(data) {
  console.log("Rendering dashboard with data:", data);

  try {
    // Health Status
    const healthHtml = `
      <div class="metric">
        <span>Overall Status</span>
        <span class="metric-value">
          <span class="status-indicator status-${
            data.indicators?.overall || "unknown"
          }"></span>
          ${(data.indicators?.overall || "unknown").toUpperCase()}
        </span>
      </div>
      <div class="metric">
        <span>Uptime</span>
        <span class="metric-value">${formatUptime(
          data.overview?.uptime || 0
        )}</span>
      </div>
    `;
    document.getElementById("health-status").innerHTML = healthHtml;

    // Overview Metrics
    const overviewHtml = `
      <div class="metric">
        <span>Total Scrapes</span>
        <span class="metric-value">${data.overview?.totalScrapes || 0}</span>
      </div>
      <div class="metric">
        <span>Success Rate</span>
        <span class="metric-value">${data.overview?.successRate || "0%"}</span>
      </div>
      <div class="metric">
        <span>Failed Scrapes</span>
        <span class="metric-value">${data.overview?.failedScrapes || 0}</span>
      </div>
    `;
    document.getElementById("overview-metrics").innerHTML = overviewHtml;

    // Performance Metrics
    const performanceHtml = `
      <div class="metric">
        <span>Avg Scraping Time</span>
        <span class="metric-value">${
          data.performance?.averageScrapingTime || "0s"
        }</span>
      </div>
      <div class="metric">
        <span>Listings per Scrape</span>
        <span class="metric-value">${
          data.performance?.avgListingsPerScrape || "0"
        }</span>
      </div>
      <div class="metric">
        <span>Total Listings Found</span>
        <span class="metric-value">${
          data.performance?.totalListingsFound || 0
        }</span>
      </div>
      <div class="metric">
        <span>Duplicate Rate</span>
        <span class="metric-value">${
          data.performance?.duplicateRate || "0%"
        }</span>
      </div>
    `;
    document.getElementById("performance-metrics").innerHTML = performanceHtml;

    // Recent Activity
    const activityHtml = `
      <div class="metric">
        <span>Last Scrape</span>
        <span class="metric-value">
          ${
            data.recentActivity?.lastScrapeTime
              ? new Date(data.recentActivity.lastScrapeTime).toLocaleString()
              : "Never"
          }
        </span>
      </div>
      <div class="metric">
        <span>Time Since Last Scrape</span>
        <span class="metric-value">
          ${
            data.recentActivity?.timeSinceLastScrape
              ? data.recentActivity.timeSinceLastScrape + " minutes ago"
              : "N/A"
          }
        </span>
      </div>
    `;
    document.getElementById("recent-activity").innerHTML = activityHtml;
  } catch (error) {
    console.error("Error rendering dashboard:", error);
    document.getElementById(
      "loading"
    ).innerHTML = `<div class="alert alert-critical">Error rendering dashboard: ${error.message}</div>`;
  }
}

function renderAlerts(alertsData) {
  const alertsContainer = document.getElementById("alerts-container");

  if (alertsData.alerts.length === 0) {
    alertsContainer.innerHTML =
      '<p style="color: #27ae60;">✅ No active alerts</p>';
    return;
  }

  const alertsHtml = alertsData.alerts
    .map(
      (alert) => `
    <div class="alert alert-${alert.level}">
      <strong>${alert.level.toUpperCase()}:</strong> ${alert.message}
      <br><small>Type: ${alert.type} | ${new Date(
        alert.timestamp
      ).toLocaleString()}</small>
    </div>
  `
    )
    .join("");

  alertsContainer.innerHTML = alertsHtml;
}

function formatUptime(seconds) {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (days > 0) return `${days}d ${hours}h ${minutes}m`;
  if (hours > 0) return `${hours}h ${minutes}m`;
  return `${minutes}m`;
}

// Initialize dashboard when DOM is loaded
document.addEventListener("DOMContentLoaded", function () {
  console.log("DOM loaded, initializing dashboard...");
  
  // Add event listener for refresh button
  const refreshBtn = document.querySelector(".refresh-btn");
  if (refreshBtn) {
    refreshBtn.addEventListener("click", loadDashboard);
  }
  
  // Load dashboard initially
  loadDashboard();
  
  // Auto-refresh every 30 seconds
  setInterval(loadDashboard, 30000);
});
