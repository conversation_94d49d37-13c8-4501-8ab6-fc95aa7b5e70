<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Simple Monitoring Dashboard</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
        background-color: #f5f5f5;
      }
      .container {
        max-width: 1200px;
        margin: 0 auto;
      }
      .card {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .metric {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-bottom: 1px solid #eee;
      }
      .metric:last-child {
        border-bottom: none;
      }
      .status-good {
        color: #27ae60;
      }
      .status-warning {
        color: #f39c12;
      }
      .status-critical {
        color: #e74c3c;
      }
      .loading {
        text-align: center;
        padding: 40px;
      }
      .error {
        background: #f8d7da;
        color: #721c24;
        padding: 15px;
        border-radius: 4px;
      }
      .success {
        background: #d4edda;
        color: #155724;
        padding: 15px;
        border-radius: 4px;
      }
      button {
        background: #3498db;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        margin: 10px 5px;
      }
      button:hover {
        background: #2980b9;
      }
      pre {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 4px;
        overflow-x: auto;
        font-size: 12px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🏠 Property Scraper Monitoring Dashboard (Simple)</h1>

      <div class="card">
        <h2>Controls</h2>
        <button onclick="loadDashboard()">🔄 Refresh Dashboard</button>
        <button onclick="testAPI()">🧪 Test API</button>
        <button onclick="clearResults()">🗑️ Clear Results</button>
      </div>

      <div id="status" class="card">
        <h2>Status</h2>
        <div id="status-content">Ready to load...</div>
      </div>

      <div id="results" class="card">
        <h2>Results</h2>
        <div id="results-content">No data loaded yet.</div>
      </div>

      <div id="debug" class="card">
        <h2>Debug Information</h2>
        <div id="debug-content">Debug info will appear here...</div>
      </div>
    </div>

    <script>
      function log(message) {
        console.log(message);
        const debugDiv = document.getElementById("debug-content");
        debugDiv.innerHTML += `<div>${new Date().toLocaleTimeString()}: ${message}</div>`;
      }

      function setStatus(message, type = "info") {
        const statusDiv = document.getElementById("status-content");
        const className =
          type === "error" ? "error" : type === "success" ? "success" : "";
        statusDiv.innerHTML = `<div class="${className}">${message}</div>`;
      }

      function clearResults() {
        document.getElementById("results-content").innerHTML =
          "Results cleared.";
        document.getElementById("debug-content").innerHTML = "Debug cleared.";
        setStatus("Results cleared.");
      }

      async function testAPI() {
        log("Testing API endpoints...");
        setStatus("Testing API endpoints...", "info");

        const endpoints = [
          "/api/monitoring/health",
          "/api/monitoring/dashboard",
          "/api/monitoring/alerts",
        ];

        const results = {};

        for (const endpoint of endpoints) {
          try {
            log(`Testing ${endpoint}...`);
            const response = await fetch(endpoint);
            log(`${endpoint} - Status: ${response.status}`);

            if (response.ok) {
              const data = await response.json();
              results[endpoint] = { status: "success", data };
              log(`${endpoint} - Success`);
            } else {
              results[endpoint] = {
                status: "error",
                error: `HTTP ${response.status}`,
              };
              log(`${endpoint} - Error: HTTP ${response.status}`);
            }
          } catch (error) {
            results[endpoint] = { status: "error", error: error.message };
            log(`${endpoint} - Error: ${error.message}`);
          }
        }

        document.getElementById(
          "results-content"
        ).innerHTML = `<pre>${JSON.stringify(results, null, 2)}</pre>`;

        const successCount = Object.values(results).filter(
          (r) => r.status === "success"
        ).length;
        setStatus(
          `API Test Complete: ${successCount}/${endpoints.length} endpoints working`,
          successCount === endpoints.length ? "success" : "error"
        );
      }

      async function loadDashboard() {
        log("Loading dashboard data...");
        setStatus("Loading dashboard data...", "info");

        try {
          // Test dashboard endpoint
          log("Fetching dashboard data...");
          const dashboardResponse = await fetch("/api/monitoring/dashboard");
          log(`Dashboard response status: ${dashboardResponse.status}`);

          if (!dashboardResponse.ok) {
            throw new Error(
              `Dashboard API failed: HTTP ${dashboardResponse.status}`
            );
          }

          const dashboardData = await dashboardResponse.json();
          log("Dashboard data received successfully");

          // Test alerts endpoint
          log("Fetching alerts data...");
          const alertsResponse = await fetch("/api/monitoring/alerts");
          log(`Alerts response status: ${alertsResponse.status}`);

          if (!alertsResponse.ok) {
            throw new Error(`Alerts API failed: HTTP ${alertsResponse.status}`);
          }

          const alertsData = await alertsResponse.json();
          log("Alerts data received successfully");

          // Render the data
          renderSimpleDashboard(dashboardData.data, alertsData.data);
          setStatus("Dashboard loaded successfully!", "success");
        } catch (error) {
          log(`Error loading dashboard: ${error.message}`);
          setStatus(`Error loading dashboard: ${error.message}`, "error");
          document.getElementById(
            "results-content"
          ).innerHTML = `<div class="error">Failed to load dashboard: ${error.message}</div>`;
        }
      }

      function renderSimpleDashboard(dashboardData, alertsData) {
        log("Rendering dashboard...");

        const html = `
                <h3>📊 Overview</h3>
                <div class="metric">
                    <span>Total Scrapes</span>
                    <span>${dashboardData.overview?.totalScrapes || 0}</span>
                </div>
                <div class="metric">
                    <span>Success Rate</span>
                    <span class="status-${getStatusClass(
                      parseFloat(dashboardData.overview?.successRate) || 0
                    )}">${dashboardData.overview?.successRate || "0%"}</span>
                </div>
                <div class="metric">
                    <span>Failed Scrapes</span>
                    <span>${dashboardData.overview?.failedScrapes || 0}</span>
                </div>
                
                <h3>⚡ Performance</h3>
                <div class="metric">
                    <span>Average Scraping Time</span>
                    <span>${
                      dashboardData.performance?.averageScrapingTime || "N/A"
                    }</span>
                </div>
                <div class="metric">
                    <span>Listings per Scrape</span>
                    <span>${
                      dashboardData.performance?.avgListingsPerScrape || "0"
                    }</span>
                </div>
                <div class="metric">
                    <span>Total Listings Found</span>
                    <span>${
                      dashboardData.performance?.totalListingsFound || 0
                    }</span>
                </div>
                
                <h3>🚨 Alerts</h3>
                <div class="metric">
                    <span>Active Alerts</span>
                    <span class="status-${
                      alertsData.alertCount > 0 ? "warning" : "good"
                    }">${alertsData.alertCount || 0}</span>
                </div>
                
                <h3>🔍 Recent Activity</h3>
                <div class="metric">
                    <span>Last Scrape</span>
                    <span>${
                      dashboardData.recentActivity?.lastScrapeTime
                        ? new Date(
                            dashboardData.recentActivity.lastScrapeTime
                          ).toLocaleString()
                        : "Never"
                    }</span>
                </div>
                
                <h3>📋 Raw Data</h3>
                <pre>${JSON.stringify(
                  { dashboard: dashboardData, alerts: alertsData },
                  null,
                  2
                )}</pre>
            `;

        document.getElementById("results-content").innerHTML = html;
        log("Dashboard rendered successfully");
      }

      function getStatusClass(successRate) {
        if (successRate >= 80) return "good";
        if (successRate >= 50) return "warning";
        return "critical";
      }

      // Auto-load on page load
      window.onload = function () {
        log("Page loaded, auto-loading dashboard...");
        loadDashboard();
      };
    </script>
  </body>
</html>
