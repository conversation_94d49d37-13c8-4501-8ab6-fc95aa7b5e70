const puppeteer = require('puppeteer');
const cheerio = require('cheerio');

// Debug script to analyze price extraction on individual listing pages
async function debugPriceExtraction() {
  const testUrls = [
    'https://www.funda.nl/detail/huur/veendam/huis-meezenbroekstraat-47/43062620/',
    'https://www.funda.nl/detail/huur/wassenaar/huis-klingelaan-49/43062474/',
    'https://www.funda.nl/detail/huur/aalsmeer/appartement-kas-59/43901633/'
  ];

  const browser = await puppeteer.launch({
    headless: false,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-infobars',
      '--window-position=0,0',
      '--ignore-certificate-errors',
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-renderer-backgrounding',
      '--disable-dev-shm-usage',
      '--disable-extensions',
    ],
  });

  for (const url of testUrls) {
    console.log(`\n=== ANALYZING: ${url} ===`);
    
    try {
      const page = await browser.newPage();
      
      // Set user agent
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
      
      await page.goto(url, {
        waitUntil: 'networkidle2',
        timeout: 30000,
      });

      // Wait for content to load
      await new Promise(r => setTimeout(r, 3000));

      const html = await page.content();
      const $ = cheerio.load(html);

      console.log('Page title:', $('title').text());

      // Try all possible price selectors
      const priceSelectors = [
        '[data-test-id="price-rent"]',
        '[data-test-id="price"]',
        '.object-header__price',
        '.price-label',
        '.fd-m-price',
        '.object-header__price-label',
        '.object-price',
        '.price',
        '[class*="price"]',
        '[data-testid*="price"]',
        '.kenmerken-price',
        '.object-kenmerken-price'
      ];

      console.log('\n--- PRICE SELECTOR ANALYSIS ---');
      let priceFound = false;
      
      for (const selector of priceSelectors) {
        const elements = $(selector);
        if (elements.length > 0) {
          elements.each((i, el) => {
            const text = $(el).text().trim();
            if (text) {
              console.log(`✓ ${selector}: "${text}"`);
              priceFound = true;
            }
          });
        }
      }

      if (!priceFound) {
        console.log('❌ No price found with standard selectors');
      }

      // Search for price patterns in the entire HTML
      console.log('\n--- PRICE PATTERN SEARCH ---');
      const pricePatterns = [
        /€\s*[\d.,]+\s*per\s*maand/gi,
        /€\s*[\d.,]+\s*\/\s*maand/gi,
        /€\s*[\d.,]+\s*p\.m\./gi,
        /€\s*[\d.,]+/g,
        /prijs\s*op\s*aanvraag/gi,
        /huurprijs[:\s]*€?\s*[\d.,]+/gi
      ];

      for (const pattern of pricePatterns) {
        const matches = html.match(pattern);
        if (matches) {
          console.log(`Pattern ${pattern}: Found ${matches.length} matches`);
          matches.slice(0, 5).forEach(match => console.log(`  - "${match}"`));
        }
      }

      // Look for JSON-LD data on the detail page
      console.log('\n--- JSON-LD DATA SEARCH ---');
      const jsonLdScripts = $('script[type="application/ld+json"]');
      console.log(`Found ${jsonLdScripts.length} JSON-LD scripts`);
      
      jsonLdScripts.each((i, script) => {
        try {
          const jsonData = JSON.parse($(script).html());
          console.log(`Script ${i + 1}:`, JSON.stringify(jsonData, null, 2).substring(0, 500) + '...');
          
          // Look for price-related fields
          const jsonStr = JSON.stringify(jsonData);
          if (jsonStr.includes('price') || jsonStr.includes('Price') || jsonStr.includes('€')) {
            console.log('  ✓ Contains price-related data');
          }
        } catch (e) {
          console.log(`Script ${i + 1}: Invalid JSON`);
        }
      });

      // Save the page HTML for manual inspection
      const fs = require('fs');
      const filename = `debug_${url.split('/').pop()}.html`;
      fs.writeFileSync(filename, html);
      console.log(`\n💾 Saved page HTML to: ${filename}`);

      await page.close();
      
    } catch (error) {
      console.error(`Error analyzing ${url}:`, error.message);
    }
  }

  await browser.close();
}

// Run the debug script
debugPriceExtraction().catch(console.error);
