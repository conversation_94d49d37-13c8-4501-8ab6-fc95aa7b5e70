<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Funda Scraper Monitoring Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .card h3 {
            margin-bottom: 1rem;
            color: #2c3e50;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .metric-value {
            font-weight: bold;
            font-size: 1.1em;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-good { background-color: #27ae60; }
        .status-warning { background-color: #f39c12; }
        .status-critical { background-color: #e74c3c; }
        .status-unknown { background-color: #95a5a6; }
        
        .alert {
            padding: 1rem;
            margin: 0.5rem 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        
        .alert-warning {
            background-color: #fff3cd;
            border-color: #f39c12;
            color: #856404;
        }
        
        .alert-critical {
            background-color: #f8d7da;
            border-color: #e74c3c;
            color: #721c24;
        }
        
        .refresh-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            margin-bottom: 1rem;
        }
        
        .refresh-btn:hover {
            background: #2980b9;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }
        
        .timestamp {
            font-size: 0.9em;
            color: #666;
            text-align: center;
            margin-top: 1rem;
        }
        
        .chart-container {
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 4px;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏠 Funda Scraper Monitoring Dashboard</h1>
        <p>Real-time monitoring of scraping performance and health</p>
    </div>
    
    <div class="container">
        <button class="refresh-btn" id="refresh-btn">🔄 Refresh Data</button>
        
        <div id="loading" class="loading">
            Loading dashboard data...
        </div>
        
        <div id="dashboard" style="display: none;">
            <!-- Health Status -->
            <div class="card">
                <h3>🏥 System Health</h3>
                <div id="health-status"></div>
            </div>
            
            <!-- Overview Metrics -->
            <div class="dashboard-grid">
                <div class="card">
                    <h3>📊 Overview</h3>
                    <div id="overview-metrics"></div>
                </div>
                
                <div class="card">
                    <h3>⚡ Performance</h3>
                    <div id="performance-metrics"></div>
                </div>
                
                <div class="card">
                    <h3>🔍 Recent Activity</h3>
                    <div id="recent-activity"></div>
                </div>
            </div>
            
            <!-- Alerts -->
            <div class="card">
                <h3>🚨 Alerts</h3>
                <div id="alerts-container"></div>
            </div>
            
            <!-- Charts -->
            <div class="dashboard-grid">
                <div class="card">
                    <h3>📈 Success vs Failure</h3>
                    <div id="success-chart" class="chart-container">
                        Chart visualization would go here
                    </div>
                </div>
                
                <div class="card">
                    <h3>🐛 Error Distribution</h3>
                    <div id="error-chart" class="chart-container">
                        Chart visualization would go here
                    </div>
                </div>
            </div>
        </div>
        
        <div class="timestamp" id="last-updated"></div>
    </div>

    <script src="/js/monitoring-dashboard.js"></script>
</body>
</html>
