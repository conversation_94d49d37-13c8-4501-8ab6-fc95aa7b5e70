<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Funda Scraper Monitoring Dashboard</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background-color: #f5f5f5;
        color: #333;
      }

      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        text-align: center;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
      }

      .dashboard-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
      }

      .card {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .card h3 {
        margin-bottom: 1rem;
        color: #2c3e50;
      }

      .metric {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #eee;
      }

      .metric:last-child {
        border-bottom: none;
      }

      .metric-value {
        font-weight: bold;
        font-size: 1.1em;
      }

      .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
      }

      .status-good {
        background-color: #27ae60;
      }
      .status-warning {
        background-color: #f39c12;
      }
      .status-critical {
        background-color: #e74c3c;
      }

      .alert {
        padding: 1rem;
        margin: 0.5rem 0;
        border-radius: 4px;
        border-left: 4px solid;
      }

      .alert-warning {
        background-color: #fff3cd;
        border-color: #f39c12;
        color: #856404;
      }

      .alert-critical {
        background-color: #f8d7da;
        border-color: #e74c3c;
        color: #721c24;
      }

      .refresh-btn {
        background: #3498db;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 4px;
        cursor: pointer;
        font-size: 1rem;
        margin-bottom: 1rem;
      }

      .refresh-btn:hover {
        background: #2980b9;
      }

      .loading {
        text-align: center;
        padding: 2rem;
        color: #666;
      }

      .timestamp {
        font-size: 0.9em;
        color: #666;
        text-align: center;
        margin-top: 1rem;
      }

      .chart-container {
        height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f8f9fa;
        border-radius: 4px;
        margin-top: 1rem;
      }
    </style>
  </head>
  <body>
    <div class="header">
      <h1>🏠 Funda Scraper Monitoring Dashboard</h1>
      <p>Real-time monitoring of scraping performance and health</p>
    </div>

    <div class="container">
      <button class="refresh-btn" id="refresh-btn">🔄 Refresh Data</button>

      <div id="loading" class="loading">Loading dashboard data...</div>

      <div id="dashboard" style="display: none">
        <!-- Health Status -->
        <div class="card">
          <h3>🏥 System Health</h3>
          <div id="health-status"></div>
        </div>

        <!-- Overview Metrics -->
        <div class="dashboard-grid">
          <div class="card">
            <h3>📊 Overview</h3>
            <div id="overview-metrics"></div>
          </div>

          <div class="card">
            <h3>⚡ Performance</h3>
            <div id="performance-metrics"></div>
          </div>

          <div class="card">
            <h3>🔍 Recent Activity</h3>
            <div id="recent-activity"></div>
          </div>
        </div>

        <!-- Alerts -->
        <div class="card">
          <h3>🚨 Alerts</h3>
          <div id="alerts-container"></div>
        </div>

        <!-- Charts -->
        <div class="dashboard-grid">
          <div class="card">
            <h3>📈 Success vs Failure</h3>
            <div id="success-chart" class="chart-container">
              Chart visualization would go here
            </div>
          </div>

          <div class="card">
            <h3>🐛 Error Distribution</h3>
            <div id="error-chart" class="chart-container">
              Chart visualization would go here
            </div>
          </div>
        </div>
      </div>

      <div class="timestamp" id="last-updated"></div>
    </div>

    <script>
      async function loadDashboard() {
        console.log("Loading dashboard...");
        document.getElementById("loading").style.display = "block";
        document.getElementById("dashboard").style.display = "none";

        try {
          console.log("Fetching dashboard data...");
          // Load dashboard data
          const response = await fetch("/api/monitoring/dashboard");
          console.log("Dashboard response status:", response.status);

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();
          console.log("Dashboard data:", data);

          if (data.status === "success") {
            renderDashboard(data.data);
            document.getElementById("loading").style.display = "none";
            document.getElementById("dashboard").style.display = "block";
            document.getElementById(
              "last-updated"
            ).textContent = `Last updated: ${new Date().toLocaleString()}`;
          }

          console.log("Fetching alerts...");
          // Load alerts
          const alertsResponse = await fetch("/api/monitoring/alerts");
          console.log("Alerts response status:", alertsResponse.status);

          if (alertsResponse.ok) {
            const alertsData = await alertsResponse.json();
            console.log("Alerts data:", alertsData);

            if (alertsData.status === "success") {
              renderAlerts(alertsData.data);
            }
          }
        } catch (error) {
          console.error("Error loading dashboard:", error);
          document.getElementById(
            "loading"
          ).innerHTML = `<div class="alert alert-critical">Failed to load dashboard data: ${error.message}</div>`;
        }
      }

      function renderDashboard(data) {
        console.log("Rendering dashboard with data:", data);

        try {
          // Health Status
          const healthHtml = `
                    <div class="metric">
                        <span>Overall Status</span>
                        <span class="metric-value">
                            <span class="status-indicator status-${
                              data.indicators?.overall || "unknown"
                            }"></span>
                            ${(
                              data.indicators?.overall || "unknown"
                            ).toUpperCase()}
                        </span>
                    </div>
                    <div class="metric">
                        <span>Uptime</span>
                        <span class="metric-value">${formatUptime(
                          data.overview?.uptime || 0
                        )}</span>
                    </div>
                `;
          document.getElementById("health-status").innerHTML = healthHtml;

          // Overview Metrics
          const overviewHtml = `
                <div class="metric">
                    <span>Total Scrapes</span>
                    <span class="metric-value">${
                      data.overview?.totalScrapes || 0
                    }</span>
                </div>
                <div class="metric">
                    <span>Success Rate</span>
                    <span class="metric-value">${
                      data.overview?.successRate || "0%"
                    }</span>
                </div>
                <div class="metric">
                    <span>Failed Scrapes</span>
                    <span class="metric-value">${
                      data.overview?.failedScrapes || 0
                    }</span>
                </div>
            `;
          document.getElementById("overview-metrics").innerHTML = overviewHtml;

          // Performance Metrics
          const performanceHtml = `
                <div class="metric">
                    <span>Avg Scraping Time</span>
                    <span class="metric-value">${data.performance.averageScrapingTime}</span>
                </div>
                <div class="metric">
                    <span>Listings per Scrape</span>
                    <span class="metric-value">${data.performance.avgListingsPerScrape}</span>
                </div>
                <div class="metric">
                    <span>Total Listings Found</span>
                    <span class="metric-value">${data.performance.totalListingsFound}</span>
                </div>
                <div class="metric">
                    <span>Duplicate Rate</span>
                    <span class="metric-value">${data.performance.duplicateRate}</span>
                </div>
            `;
          document.getElementById("performance-metrics").innerHTML =
            performanceHtml;

          // Recent Activity
          const activityHtml = `
                <div class="metric">
                    <span>Last Scrape</span>
                    <span class="metric-value">
                        ${
                          data.recentActivity.lastScrapeTime
                            ? new Date(
                                data.recentActivity.lastScrapeTime
                              ).toLocaleString()
                            : "Never"
                        }
                    </span>
                </div>
                <div class="metric">
                    <span>Time Since Last Scrape</span>
                    <span class="metric-value">
                        ${
                          data.recentActivity.timeSinceLastScrape
                            ? data.recentActivity.timeSinceLastScrape +
                              " minutes ago"
                            : "N/A"
                        }
                    </span>
                </div>
            `;
          document.getElementById("recent-activity").innerHTML = activityHtml;
        } catch (error) {
          console.error("Error rendering dashboard:", error);
          document.getElementById(
            "loading"
          ).innerHTML = `<div class="alert alert-critical">Error rendering dashboard: ${error.message}</div>`;
        }
      }

      function renderAlerts(alertsData) {
        const alertsContainer = document.getElementById("alerts-container");

        if (alertsData.alerts.length === 0) {
          alertsContainer.innerHTML =
            '<p style="color: #27ae60;">✅ No active alerts</p>';
          return;
        }

        const alertsHtml = alertsData.alerts
          .map(
            (alert) => `
                <div class="alert alert-${alert.level}">
                    <strong>${alert.level.toUpperCase()}:</strong> ${
              alert.message
            }
                    <br><small>Type: ${alert.type} | ${new Date(
              alert.timestamp
            ).toLocaleString()}</small>
                </div>
            `
          )
          .join("");

        alertsContainer.innerHTML = alertsHtml;
      }

      function formatUptime(seconds) {
        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);

        if (days > 0) return `${days}d ${hours}h ${minutes}m`;
        if (hours > 0) return `${hours}h ${minutes}m`;
        return `${minutes}m`;
      }

      // Auto-refresh every 30 seconds
      setInterval(loadDashboard, 30000);

      // Load dashboard on page load
      loadDashboard();
    </script>
  </body>
</html>
